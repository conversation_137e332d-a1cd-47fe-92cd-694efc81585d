package org.example.core.domain.repository

import kotlinx.coroutines.flow.Flow
import org.example.core.domain.model.Receipt

interface ReceiptRepository {
    suspend fun saveReceipt(receipt: Receipt)
    suspend fun getReceiptById(id: String): Receipt?
    fun getAllReceipts(): Flow<List<Receipt>>
    suspend fun deleteReceipt(id: String)
    suspend fun updateReceipt(receipt: Receipt)
    suspend fun duplicateReceipt(receipt: Receipt)
    fun searchReceiptsByStoreName(storeName: String): Flow<List<Receipt>>
    fun getReceiptsByDateRange(startDate: String, endDate: String): Flow<List<Receipt>>
    fun getReceiptsFromDate(fromDate: String): Flow<List<Receipt>>
    fun getReceiptsToDate(toDate: String): Flow<List<Receipt>>
}