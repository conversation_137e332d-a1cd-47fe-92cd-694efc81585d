package org.example.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.DateTimeUnit
import org.example.core.domain.model.Category
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import org.example.shared.component.DateFilter
import org.example.shared.component.DateFilterUtils
import kotlinx.datetime.LocalDate
import kotlinx.datetime.minus
import org.example.core.utils.DateUtils

data class ReceiptData(
    val id: String,
    val purchaseDate: String,
    val receiptSum: String = "0,00",
)

data class CategorySpending(
    val categoryName: String,
    val categorySum: Long
)

data class DaySpending(
    val dayName: String,
    val date: Int,
    val month: String,
    val localDate: LocalDate,
    val spending: Long,
    val budget: Long,
    val isOverBudget: Boolean,
    val budgetUsagePercent: Float
)

data class PreviousWeekSummary(
    val weekRange: String, // "DD.MM - DD.MM"
    val totalSpending: Long,
    val totalBudget: Long,
    val weekBudgetUsagePercent: Float,
    val isOverBudget: Boolean,
    val dailySpendings: List<DaySpending>
)

data class DashboardUiState(
    val receipts: List<ReceiptData> = emptyList(),
    val budget: Long = 0L,
    val totalSpending: Long = 0,
    val categorySpendings: List<CategorySpending> = emptyList(),
    val currentFilter: DateFilter = DateFilter.Today /*DateFilter.ThisWeek*/, /* TODO ten current filter nie jest polaczony z zaznaczeniem w UI. Wczesniej bylo ThisWeek . W wywolaniu komponentu CurrentWeekBudget jest  selectedDate = uiState.selectedDate; a w param komponentu jest default jako null selectedDate: LocalDate? = null */
    val isFilterExpanded: Boolean = false,
    val availableFilters: List<DateFilter> = DateFilter.getDefaultFilters(),
    val availableCategories: List<Category> = emptyList(),
    val selectedDate: LocalDate? = null,
    val remainingBudget: Long = 0L,
    val budgetProgress: Float = 0f,
    val isBudgetExceeded: Boolean = false,
    // Nowe pola dla adaptacyjnego budżetu
    val adaptiveDailyBudget: Long = 0L,
    val budgetProgressDetails: BudgetProgress? = null,
    val spendingRecommendation: SpendingRecommendation? = null,
    val isAdaptiveBudgetActive: Boolean = true,
    val budgetType: BudgetType = BudgetType.BASE,
    // Dane z poprzedniego tygodnia
    val previousWeekSummary: PreviousWeekSummary? = null
)

class DashboardViewModel(
    private val getReceiptsUseCase: GetReceiptsUseCase,
    private val getTypesAndCategoriesUseCase: GetTypesAndCategoriesUseCase
) : ViewModel() {
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    init {
        observeReceipts()
        observeCategories()
        loadPreviousWeekSummary()
    }

    private fun observeCategories() {
        viewModelScope.launch {
            getTypesAndCategoriesUseCase.getCategories().collect { categories ->
                _uiState.update { it.copy(availableCategories = categories) }
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun observeReceipts() {
        viewModelScope.launch {
            _uiState
                .map { it.currentFilter }
                .distinctUntilChanged()
                .flatMapLatest { filter ->
                    val dateRange = DateFilterUtils.getDateRange(filter)
                    getReceiptsUseCase.getReceiptsWithDateFilter(
                        dateRange.startDate,
                        dateRange.endDate
                    )
                }
                .collect { receipts ->
                    val currentFilter = _uiState.value.currentFilter
                    updateBudgetCalculations(receipts, currentFilter)
                }
        }
    }

    private suspend fun updateBudgetCalculations(
        receipts: List<Receipt>,
        currentFilter: DateFilter
    ) {
        println("DEBUG updateBudgetCalculations: filter=$currentFilter")

        val baseBudget = BudgetManager.calculateBudgetForDateFilter(currentFilter)
        val totalSpending = calculateTotalSpending(receipts)

        println("DEBUG updateBudgetCalculations: baseBudget=$baseBudget, totalSpending=$totalSpending")

        // Sprawdź czy to jest filtr adaptacyjnego budżetu
        val isAdaptiveBudgetActive = shouldUseAdaptiveBudget(currentFilter)

        println("DEBUG updateBudgetCalculations: isAdaptiveBudgetActive=$isAdaptiveBudgetActive")

        // Oblicz adaptacyjny budżet dla konkretnego dnia
        val adaptiveBudgetResult = if (isAdaptiveBudgetActive) {
            calculateAdaptiveBudgetForSpecificDay(currentFilter)
        } else {
            AdaptiveBudgetResult(baseBudget, BudgetType.BASE)
        }

        val effectiveBudget = adaptiveBudgetResult.budget
        val budgetType = adaptiveBudgetResult.type

        println("DEBUG updateBudgetCalculations: effectiveBudget=$effectiveBudget, budgetType=$budgetType")

        val budgetProgressDetails = BudgetManager.calculateBudgetProgress(
            currentSpending = totalSpending,
            originalBudget = baseBudget,
            adaptiveBudget = if (isAdaptiveBudgetActive) effectiveBudget else null
        )

        val spendingRecommendation =
            if (isAdaptiveBudgetActive && budgetType == BudgetType.CURRENT_ADAPTIVE) {
                val timeOfDay = calculateTimeOfDay()
                BudgetManager.calculateRecommendedSpending(
                    currentDaySpent = totalSpending,
                    adaptiveDailyBudget = effectiveBudget,
                    timeOfDay = timeOfDay
                )
            } else null

        _uiState.update { currentState ->
            currentState.copy(
                receipts = receipts.map {
                    ReceiptData(
                        id = it.id,
                        purchaseDate = it.purchaseDate,
                        receiptSum = formatPrice(it.receiptSum ?: 0)
                    )
                },
                budget = effectiveBudget,
                totalSpending = totalSpending,
                categorySpendings = calculateCategorySpendings(receipts),
                remainingBudget = budgetProgressDetails.remainingBudget,
                budgetProgress = budgetProgressDetails.progress,
                isBudgetExceeded = budgetProgressDetails.isExceeded,
                adaptiveDailyBudget = effectiveBudget,
                budgetProgressDetails = budgetProgressDetails,
                spendingRecommendation = spendingRecommendation,
                isAdaptiveBudgetActive = isAdaptiveBudgetActive,
                budgetType = budgetType
            )
        }

        println("DEBUG updateBudgetCalculations: Updated UI state with budget=$effectiveBudget")
    }

    /**
     * Calculates the adaptive budget for a specific day based on the provided date filter.
     *
     * This function determines the target day from the filter and then calculates the spending
     * up to the day before the target day and up to the target day itself within the current week.
     * It then uses these values, along with the current day of the week and the target day of the week,
     * to calculate the adaptive budget using [BudgetManager.calculateAdaptiveBudgetForSpecificDay].
     *
     * @param filter The [DateFilter] used to determine the target day.
     *               If [DateFilter.Today] or a [DateFilter.Custom] for today, the target is today.
     *               If [DateFilter.Custom] for another day, that day is the target.
     *               Otherwise, defaults to today.
     * @return An [AdaptiveBudgetResult] containing the calculated adaptive budget and its type.
     */
    private suspend fun calculateAdaptiveBudgetForSpecificDay(filter: DateFilter): AdaptiveBudgetResult {
        val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
        val todayTimestamp = DateUtils.getCurrentTimestamp()
        val startOfWeek = DateUtils.getStartOfWeek(todayTimestamp)
        val currentDayOfWeek =
            today.dayOfWeek.ordinal + 1 // kotlinx-datetime: MONDAY = 0, więc +1 = 1-7

        // Określ dzień docelowy na podstawie filtra
        val targetDate = when (filter) {
            is DateFilter.Today -> today
            is DateFilter.Custom -> {
                if (filter.startDate != null) {
                    DateUtils.timestampToLocalDate(DateUtils.isoStringToTimestamp(filter.startDate!!))
                } else {
                    today
                }
            }

            else -> today
        }

        val targetDayOfWeek = targetDate.dayOfWeek.ordinal + 1

        println("DEBUG: calculateAdaptiveBudgetForSpecificDay - targetDate=$targetDate, targetDayOfWeek=$targetDayOfWeek, currentDayOfWeek=$currentDayOfWeek")

        // Oblicz wydatki do końca dnia poprzedzającego dzień docelowy
        val previousDayEnd = if (targetDayOfWeek > 1) {
            val previousDay = targetDate.minus(1, DateTimeUnit.DAY)
            val previousDayTimestamp = DateUtils.localDateToTimestamp(previousDay)
            DateUtils.getEndOfDay(previousDayTimestamp)
        } else {
            startOfWeek - 1 // Przed początkiem tygodnia
        }

        // Oblicz wydatki do końca dnia docelowego
        val targetDayEnd = DateUtils.getEndOfDay(DateUtils.localDateToTimestamp(targetDate))

        val weeklySpentUpToPreviousDay = if (targetDayOfWeek > 1) {
            getSpendingForDateRange(startOfWeek, previousDayEnd)
        } else {
            0L // Poniedziałek - brak poprzednich dni
        }

        val weeklySpentUpToTargetDay = getSpendingForDateRange(startOfWeek, targetDayEnd)

        println("DEBUG: weeklySpentUpToPreviousDay=$weeklySpentUpToPreviousDay, weeklySpentUpToTargetDay=$weeklySpentUpToTargetDay")

        return BudgetManager.calculateAdaptiveBudgetForSpecificDay(
            targetDayOfWeek = targetDayOfWeek,
            currentDayOfWeek = currentDayOfWeek,
            weeklySpentUpToTargetDay = weeklySpentUpToTargetDay,
            weeklySpentUpToPreviousDay = weeklySpentUpToPreviousDay
        )
    }

    private suspend fun calculateAdaptiveBudgetForToday(): Long {
        val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
        val todayTimestamp = DateUtils.getCurrentTimestamp()
        val startOfWeek = DateUtils.getStartOfWeek(todayTimestamp)

        // Jeśli to poniedziałek, zwróć bazowy budżet
        if (today.dayOfWeek == DayOfWeek.MONDAY) {
            println("DEBUG: Monday - returning base budget")
            return BudgetManager.calculateBudgetForDateFilter(DateFilter.Today)
        }

        // Pobierz wydatki od początku tygodnia do wczoraj (WYŁĄCZNIE)
        val yesterdayEnd = DateUtils.getYesterdayEnd()
        val weeklySpentSoFar = getSpendingForDateRange(startOfWeek, yesterdayEnd)
        val dayOfWeek = today.dayOfWeek.ordinal + 1 // kotlinx-datetime: MONDAY = 0, więc +1 = 1-7

        println("DEBUG: weeklySpentSoFar=$weeklySpentSoFar, dayOfWeek=$dayOfWeek")

        val adaptiveBudget = BudgetManager.calculateAdaptiveDailyBudget(
            weeklySpentSoFar = weeklySpentSoFar,
            dayOfWeek = dayOfWeek
        )

        println("DEBUG: Adaptive budget calculated: $adaptiveBudget")
        return adaptiveBudget
    }

    private suspend fun getSpendingForDateRange(startTimestamp: Long, endTimestamp: Long): Long {
        return getReceiptsUseCase.getReceiptsWithDateFilter(
            DateUtils.timestampToIsoString(startTimestamp),
            DateUtils.timestampToIsoString(endTimestamp)
        ).first().let { receipts ->
            calculateTotalSpending(receipts)
        }
    }

    private fun isCurrentDay(filter: DateFilter): Boolean {
        return when (filter) {
            is DateFilter.Today -> true
            is DateFilter.Custom -> {
                // Sprawdź czy custom filter to dzisiaj
                if (filter.startDate != null) {
                    val todayTimestamp = DateUtils.getCurrentTimestamp()
                    val filterTimestamp = DateUtils.isoStringToTimestamp(filter.startDate!!)
                    DateUtils.isSameDay(todayTimestamp, filterTimestamp)
                } else {
                    false
                }
            }

            else -> false
        }
    }

    private fun shouldUseAdaptiveBudget(filter: DateFilter): Boolean {
        return when (filter) {
            is DateFilter.Today -> true
            is DateFilter.ThisWeek -> false // Dla tygodnia używamy stałego budżetu
            is DateFilter.Custom -> {
                // Dla custom sprawdź czy to jeden dzień w obecnym tygodniu
                val bol = filter.startDate != null && filter.endDate != null &&
                        isWithinCurrentWeek(filter.startDate!!) &&
                        isSingleDayRange(filter.startDate!!, filter.endDate!!)
                return bol
            }

            else -> false
        }
    }

    private fun isWithinCurrentWeek(dateString: String): Boolean {
        return try {
            val targetTimestamp = DateUtils.isoStringToTimestamp(dateString)
            val todayTimestamp = DateUtils.getCurrentTimestamp()
            val startOfWeek = DateUtils.getStartOfWeek(todayTimestamp)
            val endOfWeek = DateUtils.getEndOfWeek(todayTimestamp)

            targetTimestamp >= startOfWeek && targetTimestamp <= endOfWeek
        } catch (e: Exception) {
            false
        }
    }

    private fun isSingleDayRange(startDateString: String, endDateString: String): Boolean {
        return try {
            val startTimestamp = DateUtils.isoStringToTimestamp(startDateString)
            val endTimestamp = DateUtils.isoStringToTimestamp(endDateString)
            DateUtils.isSingleDayRange(startTimestamp, endTimestamp)
        } catch (e: Exception) {
            false
        }
    }

    private fun calculateTimeOfDay(): Float {
        return DateUtils.getCurrentTimeOfDay()
    }

    // Pozostałe metody bez zmian...
    private fun calculateTotalSpending(receipts: List<Receipt>): Long {
        val products = receipts.flatMap { it.products }
        return products.sumOf { it.totalInCents }
    }

    private fun calculateCategorySpendings(receipts: List<Receipt>): List<CategorySpending> {
        val products = receipts.flatMap { it.products }
        val categorySums = products.groupBy { it.category }
            .mapValues { entry ->
                entry.value.sumOf { it.totalInCents }
            }
        return categorySums.map { (category, sum) ->
            CategorySpending(category, sum)
        }
    }

    fun onDaySelected(selectedDate: LocalDate) {
        val currentState = _uiState.value

        if (currentState.selectedDate == selectedDate) {
            _uiState.update {
                it.copy(
                    currentFilter = DateFilter.ThisWeek,
                    selectedDate = null,
                    isFilterExpanded = false
                )
            }
        } else {
            val startOfDay = DateUtils.localDateToTimestamp(selectedDate)
            val endOfDay = DateUtils.getEndOfDay(startOfDay)

            val dayFilter = DateFilter.Custom(
                startDate = DateUtils.timestampToIsoString(startOfDay),
                endDate = DateUtils.timestampToIsoString(endOfDay)
            )

            _uiState.update {
                it.copy(
                    currentFilter = dayFilter,
                    selectedDate = selectedDate,
                    isFilterExpanded = false
                )
            }
        }
    }

    /**
     * Metoda do ręcznego przeliczenia budżetu (np. po dodaniu nowego wydatku)
     */
    fun recalculateBudget() {
        viewModelScope.launch {
            val currentFilter = _uiState.value.currentFilter
            val dateRange = DateFilterUtils.getDateRange(currentFilter)
            val receipts = getReceiptsUseCase.getReceiptsWithDateFilter(
                dateRange.startDate,
                dateRange.endDate
            ).first()

            updateBudgetCalculations(receipts, currentFilter)
        }
    }

    private fun loadPreviousWeekSummary() {
        viewModelScope.launch {
            val previousWeekSummary = calculatePreviousWeekSummary()
            _uiState.update { it.copy(previousWeekSummary = previousWeekSummary) }
        }
    }

    private suspend fun calculatePreviousWeekSummary(): PreviousWeekSummary {
        val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
        val currentWeekStart = DateUtils.getStartOfWeek(DateUtils.getCurrentTimestamp())

        // Poprzedni tydzień: od poniedziałku do niedzieli
        val previousWeekEnd = currentWeekStart - 1 // Koniec poprzedniego tygodnia (niedziela)
        val previousWeekStart = DateUtils.getStartOfWeek(previousWeekEnd)

        println(
            "DEBUG: Previous week range: ${DateUtils.timestampToIsoString(previousWeekStart)} - ${
                DateUtils.timestampToIsoString(
                    previousWeekEnd
                )
            }"
        )

        // Pobierz wszystkie wydatki z poprzedniego tygodnia
        val previousWeekReceipts = getReceiptsUseCase.getReceiptsWithDateFilter(
            DateUtils.timestampToIsoString(previousWeekStart),
            DateUtils.timestampToIsoString(previousWeekEnd)
        ).first()

        val totalSpending = calculateTotalSpending(previousWeekReceipts)
        val totalBudget = BudgetManager.calculateBudgetForDateFilter(DateFilter.ThisWeek)
        val weekBudgetUsagePercent =
            if (totalBudget > 0) (totalSpending.toFloat() / totalBudget) * 100f else 0f

        // Oblicz wydatki dla każdego dnia poprzedniego tygodnia
        val dailySpendings = calculateDailySpendings(previousWeekStart, previousWeekReceipts)

        // Format zakresu dat
        val startDate = DateUtils.timestampToLocalDate(previousWeekStart)
        val endDate = DateUtils.timestampToLocalDate(previousWeekEnd)
        val weekRange = "${startDate.dayOfMonth.toString().padStart(2, '0')}.${
            startDate.monthNumber.toString().padStart(2, '0')
        } - ${endDate.dayOfMonth.toString().padStart(2, '0')}.${
            endDate.monthNumber.toString().padStart(2, '0')
        }"

        return PreviousWeekSummary(
            weekRange = weekRange,
            totalSpending = totalSpending,
            totalBudget = totalBudget,
            weekBudgetUsagePercent = weekBudgetUsagePercent,
            isOverBudget = totalSpending > totalBudget,
            dailySpendings = dailySpendings
        )
    }

    private suspend fun calculateDailySpendings(
        weekStart: Long,
        weekReceipts: List<Receipt>
    ): List<DaySpending> {
        val dayNames = listOf("Pon", "Wto", "Śro", "Czw", "Pią", "Sob", "Nie")
        val monthNames = listOf(
            "Sty",
            "Lut",
            "Mar",
            "Kwi",
            "Maj",
            "Cze",
            "Lip",
            "Sie",
            "Wrz",
            "Paź",
            "Lis",
            "Gru"
        )

        return (0..6).map { dayOffset ->
            val dayTimestamp = weekStart + (dayOffset * 24 * 60 * 60 * 1000)
            val dayStart = DateUtils.getStartOfDay(dayTimestamp)
            val dayEnd = DateUtils.getEndOfDay(dayTimestamp)
            val localDate = DateUtils.timestampToLocalDate(dayTimestamp)

            // Filtruj wydatki dla tego dnia
            val dayReceipts = weekReceipts.filter { receipt ->
                val receiptTimestamp = DateUtils.isoStringToTimestamp(receipt.purchaseDate)
                receiptTimestamp >= dayStart && receiptTimestamp <= dayEnd
            }

            val daySpending = calculateTotalSpending(dayReceipts)
            val dayBudget = BudgetManager.calculateBudgetForDateFilter(DateFilter.Today)
            val budgetUsagePercent =
                if (dayBudget > 0) (daySpending.toFloat() / dayBudget) * 100f else 0f

            DaySpending(
                dayName = dayNames[dayOffset],
                date = localDate.dayOfMonth,
                month = monthNames[localDate.monthNumber - 1],
                localDate = localDate,
                spending = daySpending,
                budget = dayBudget,
                isOverBudget = daySpending > dayBudget,
                budgetUsagePercent = budgetUsagePercent
            )
        }
    }
}

fun formatPrice(priceInCents: Long, separator: String = ","): String {
    val baseUnit = priceInCents / 100
    val subunit = priceInCents % 100
    return "$baseUnit$separator${subunit.toString().padStart(2, '0')}"
}
