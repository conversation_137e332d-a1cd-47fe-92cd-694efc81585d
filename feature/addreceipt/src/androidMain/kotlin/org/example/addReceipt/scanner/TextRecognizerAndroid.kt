package org.example.addReceipt.scanner

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntSize
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import java.io.FileOutputStream
import kotlin.coroutines.resume
import kotlin.math.abs
import org.example.addReceipt.ocr.TextGrouper
import org.example.core.domain.model.MyBoundingBox
import org.example.core.domain.model.MyTextBlock

@Composable
actual fun rememberTextRecognizer(): TextRecognizerML {
    val context = LocalContext.current
    return remember { AndroidTextRecognizer(context) }
}

const val SKEW_THRESHOLD = .5f

class AndroidTextRecognizer(private val context: Context) : TextRecognizerML {
    private val recognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)

    // Zachowaj istniejącą metodę dla kompatybilności wstecznej
    override suspend fun recognizeText(imagePath: String): String =
        suspendCancellableCoroutine { continuation ->
            processImage(imagePath) { result ->
                if (result.errorMessage != null) {
                    continuation.resume(result.errorMessage)
                } else {
                    continuation.resume(result.groupedWordsIntoLines)
                }
            }
        }

    override suspend fun recognizeTextWithDetails(imagePath: String): OcrResult =
        suspendCancellableCoroutine { continuation ->
            processImage(imagePath) { result ->
                continuation.resume(result)
            }
        }

    private fun processImage(originalImagePath: String, callback: (OcrResult) -> Unit) {
        try {
            val originalFile = File(originalImagePath)
            if (!originalFile.exists()) {
                callback(OcrResult(errorMessage = "File not found at path $originalImagePath"))
                return
            }

            val initialImage =
                InputImage.fromFilePath(context, android.net.Uri.fromFile(originalFile))
            var finalVisionText: Text? = null
            var finalImagePathForDisplay: String = originalImagePath

            recognizer.process(initialImage)
                .addOnSuccessListener { firstPassVisionText: Text ->
                    finalVisionText = firstPassVisionText

                    val (skewAngle, foundReference) = detectSkewAngle(firstPassVisionText)

                    if (foundReference && abs(skewAngle) > SKEW_THRESHOLD) {
                        println(">>> Skew detected: $skewAngle. Attempting rotation.")
                        val rotatedImagePathAttempt = rotateImage(originalImagePath, -skewAngle)

                        if (rotatedImagePathAttempt != null) {
                            println(">>> Image rotated successfully to: $rotatedImagePathAttempt")
                            finalImagePathForDisplay = rotatedImagePathAttempt
                            val rotatedImageInput = InputImage.fromFilePath(
                                context,
                                android.net.Uri.fromFile(File(rotatedImagePathAttempt))
                            )

                            recognizer.process(rotatedImageInput)
                                .addOnSuccessListener { straightenedText ->
                                    println(">>> OCR on rotated image successful.")
                                    finalVisionText = straightenedText
                                    processFinalText(
                                        finalVisionText,
                                        finalImagePathForDisplay,
                                        callback
                                    )
                                }
                                .addOnFailureListener { e ->
                                    println(">>> Error on second OCR pass (rotated image): ${e.message}. Using first pass result.")
                                    // Błąd drugiego przejścia, finalVisionText jest już ustawiony na firstPassVisionText
                                    // finalImagePathForDisplay pozostaje originalImagePath
                                    processFinalText(
                                        finalVisionText,
                                        finalImagePathForDisplay,
                                        callback,
                                        "Error on second pass (using first pass): ${e.message}"
                                    )
                                }
                        } else {
                            println(">>> Image rotation failed. Using results from first OCR pass.")
                            // finalVisionText jest już ustawiony na firstPassVisionText
                            // finalImagePathForDisplay pozostaje originalImagePath
                            processFinalText(finalVisionText, finalImagePathForDisplay, callback)
                        }
                    } else {
                        println(">>> No significant skew detected or no reference found. Using results from first OCR pass.")
                        // finalVisionText jest już ustawiony na firstPassVisionText
                        // finalImagePathForDisplay pozostaje originalImagePath
                        processFinalText(finalVisionText, finalImagePathForDisplay, callback)
                    }
                }
                .addOnFailureListener { e ->
                    println(">>> Error on first OCR pass: ${e.message}")
                    callback(OcrResult(errorMessage = "Error on first OCR pass: ${e.message}"))

                }
        } catch (e: Exception) {
            println(">>> General error in processImage: ${e.message}")
            callback(OcrResult(errorMessage = "Error: ${e.message}"))
        }
    }

    /**
     * Helper function to process the final Text object and invoke the callback.
     */
    private fun processFinalText(
        visionText: Text?,
        imagePathForDisplay: String,
        callback: (OcrResult) -> Unit,
        potentialErrorMessage: String? = null // Dodatkowy komunikat błędu np. z nieudanego drugiego przejścia
    ) {
        if (visionText == null) {
            callback(
                OcrResult(
                    errorMessage = potentialErrorMessage ?: "OCR processing resulted in null text."
                )
            )
            return
        }

        val groupedWordsIntoLines = TextGrouper.groupWordsIntoLines(visionText) // Użyj Twojego TextGrouper
        val myTextBlocks = extractMyTextBlocks(visionText) // Twoja metoda extractMyTextBlocks
        val groupedLinesWithDetails = TextGrouper.groupWordsIntoLinesWithDetails(visionText) // Twoja metoda
//        Log.d("TextGrouper", "groupedText: $groupedText")
//        Log.d("TextGrouper", "groupedLines: $groupedLines")

        callback(
            OcrResult(
                groupedWordsIntoLines = groupedWordsIntoLines,
                myTextBlocks = myTextBlocks,
                groupedLinesWithDetails = groupedLinesWithDetails,
                imagePathForDisplay = imagePathForDisplay,
                errorMessage = potentialErrorMessage // Przekaż komunikat błędu, jeśli istnieje
            )
        )
    }

    private fun extractMyTextBlocks(visionText: Text): List<MyTextBlock> {
        val myTextBlocks = mutableListOf<MyTextBlock>()

        for (block in visionText.textBlocks) {
            for (line in block.lines) {
                for (element in line.elements) {
                    element.boundingBox?.let { androidRect ->
                        // Konwertuj android.graphics.Rect na platform-agnostic BoundingBox
                        val myBoundingBox = MyBoundingBox(
                            left = androidRect.left.toFloat(),
                            top = androidRect.top.toFloat(),
                            right = androidRect.right.toFloat(),
                            bottom = androidRect.bottom.toFloat()
                        )

                        myTextBlocks.add(
                            MyTextBlock(
                                text = element.text,
                                myBoundingBox = myBoundingBox,
                                confidence = element.confidence ?: 1f
                            )
                        )
                    }
                }
            }
        }

        return myTextBlocks
    }

    /**
     * Detects the skew angle of the receipt by finding the "PARAGON FISKALNY" text
     * @return Pair of (angle in degrees, whether reference text was found)
     */
    private fun detectSkewAngle(visionText: Text): Pair<Float, Boolean> {
        val referencePatterns = listOf("PARAGON FISKALNY", "PARAGON", "FISKALNY")
        for (block in visionText.textBlocks) {
            for (line in block.lines) {
                val lineText = line.text.trim()
                if (referencePatterns.any { pattern ->
                        lineText.contains(
                            pattern,
                            ignoreCase = true
                        )
                    }) {
                    println(">>> Found reference text: '$lineText'")
                    val angleFromMLKit = line.angle // Może być Float?
                    println(">>> Line angle from ML Kit: $angleFromMLKit")

                    // ML Kit czasem zwraca kąt w zakresie -90 do 90.
                    // Jeśli kąt jest bliski +/- 90, to może być poprawny, ale może też oznaczać, że tekst jest pionowy.
                    // Dla typowych paragonów kąty powinny być małe.
                    // Dodatkowo, jeśli ML Kit nie zwraca kąta, obliczamy go.
                    if (angleFromMLKit != null && angleFromMLKit != 0.0f /*&& abs(angleFromMLKit) < 45.0f*/) { // Opcjonalny warunek na sensowny kąt
                        return Pair(angleFromMLKit, true)
                    } else {
                        val calculatedAngle = calculateAngleFromCornerPoints(line)
                        println(">>> Calculated angle from corner points: $calculatedAngle")
                        return Pair(calculatedAngle, true)
                    }
                }
            }
        }
        return Pair(0f, false) // Nie znaleziono referencji
    }
//    private fun detectSkewAngle(visionText: Text): Pair<Float, Boolean> {
//        // Common variations of the reference text
//        val referencePatterns = listOf(
//            "PARAGON FISKALNY",
//            "PARAGON",
//            "FISKALNY"
//        )
//
//        // Search for reference text in all text blocks
//        for (block in visionText.textBlocks) {
//            for (line in block.lines) {
//                val lineText = line.text.trim()
//
//                // Check if this line contains any of our reference patterns
//                if (referencePatterns.any { pattern ->
//                        lineText.contains(pattern, ignoreCase = true) })
//
//                {
//                    println(">>> Found reference text: '$lineText'")
//
//                    // Get the angle from the line
//                    val angleFromMLKit = line.angle
//                    println(">>> Line angle from ML Kit: $angleFromMLKit")
//
//                    // If ML Kit doesn't provide a reliable angle, calculate it from corner points
//                    if (angleFromMLKit == null || angleFromMLKit == 0f) {
//                        val calculatedAngle = calculateAngleFromCornerPoints(line)
//                        println(">>> Calculated angle from corner points: $calculatedAngle")
//                        return Pair(calculatedAngle, true)
//                    }
//
//                    return Pair(angleFromMLKit, true)
//                }
//            }
//        }
//
//        return Pair(0f, false)
//    }

    /**
     * Calculates the angle of a text line from its corner points
     */
    private fun calculateAngleFromCornerPoints(line: Text.Line): Float {
        val cornerPoints = line.cornerPoints ?: return 0f
        if (cornerPoints.size < 2) return 0f

        // Use the top edge of the bounding box (points 0 and 1)
        val x1 = cornerPoints[0].x
        val y1 = cornerPoints[0].y
        val x2 = cornerPoints[1].x
        val y2 = cornerPoints[1].y

        // Calculate angle in radians and convert to degrees
        val angleRadians = kotlin.math.atan2((y2 - y1).toDouble(), (x2 - x1).toDouble())
        return Math.toDegrees(angleRadians).toFloat()
    }

    /**
     * Rotates an image by the specified angle and saves it to a new file
     * @return Path to the rotated image, or null if rotation failed
     */
    private fun rotateImage(imagePath: String, angle: Float): String? {
        try {
            // Load the original bitmap
            val originalBitmap = BitmapFactory.decodeFile(imagePath) ?: return null

            // Create a matrix for the rotation
            val matrix = Matrix()
            matrix.postRotate(angle)

            // Create a new bitmap with the rotation applied
            val rotatedBitmap = Bitmap.createBitmap(
                originalBitmap,
                0, 0,
                originalBitmap.width, originalBitmap.height,
                matrix, true
            )

            // Create a new file for the rotated image (Potrzeba jest dodawać to rotated?)
            val rotatedFile = File(
                /* parent = */ context.cacheDir,
                /* child = */ "rotated_${System.currentTimeMillis()}.jpg"
            )

            // Save the rotated bitmap to the file
            FileOutputStream(rotatedFile).use { out ->
                rotatedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
            }

            // Clean up
            if (originalBitmap != rotatedBitmap) { // Zawsze prawdą, jeśli createBitmap tworzy nowy obiekt
                if (!originalBitmap.isRecycled) originalBitmap.recycle()
            }
            if (!rotatedBitmap.isRecycled) rotatedBitmap.recycle() // Obrócona bitmapa też już nie jest potrzebna po zapisie

            println("!!!!!!!!!!!!image rotated to ${rotatedFile.absolutePath}")
            return rotatedFile.absolutePath
        } catch (e: Exception) {
            println(">>> Error rotating image: ${e.message}")
            e.printStackTrace()
            return null
        }
    }
}

// Android implementation (w androidMain)
@Composable
actual fun getBitmapSize(imagePath: String): IntSize {
    return remember(imagePath) {
        try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            IntSize(options.outWidth, options.outHeight)
        } catch (e: Exception) {
            IntSize.Zero
        }
    }
}