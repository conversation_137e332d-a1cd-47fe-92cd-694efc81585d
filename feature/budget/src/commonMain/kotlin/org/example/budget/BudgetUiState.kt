package org.example.budget

import org.example.core.domain.model.Budget
import org.example.core.domain.model.BudgetHistory

/**
 * UI state for the Budget screen
 */
data class BudgetUiState(
    val isLoading: Boolean = false,
    val selectedWeekBudget: Budget? = null,
    val budgetHistory: List<BudgetHistory> = emptyList(),
    val isEditingBudget: Boolean = false,
    val budgetInputText: String = "",
    val errorMessage: String? = null,
    val isSuccessMessageVisible: Boolean = false,
    val successMessage: String? = null
) {
    /**
     * Get formatted budget amount for display
     */
    fun getFormattedBudgetAmount(): String {
        return selectedWeekBudget?.let { budget ->
            val amount = budget.budgetAmountInCents / 100.0
            String.format("%.2f zł", amount)
        } ?: "Nie ustawiono"
    }
    
    /**
     * Check if budget is set for selected week
     */
    fun isBudgetSet(): Boolean {
        return selectedWeekBudget != null
    }
    
    /**
     * Get budget amount in cents from input text
     */
    fun getBudgetAmountInCentsFromInput(): Long? {
        return try {
            val amount = budgetInputText.replace(",", ".").toDoubleOrNull()
            amount?.let { (it * 100).toLong() }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Check if input is valid
     */
    fun isInputValid(): Boolean {
        return budgetInputText.isNotBlank() && getBudgetAmountInCentsFromInput() != null
    }
}
