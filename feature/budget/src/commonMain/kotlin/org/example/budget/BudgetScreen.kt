package org.example.budget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBack
import androidx.compose.material.icons.rounded.Circle
import androidx.compose.material.icons.rounded.Today
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus
import org.example.core.utils.DateUtils
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BudgetScreen(
    navigateBack: () -> Unit,
    viewModel: BudgetViewModel = koinViewModel()
) {
    val currentWeekIndex = remember { getCurrentWeekIndex() }
    var selectedWeekIndex by remember { mutableIntStateOf(currentWeekIndex) }
    val listState = rememberLazyListState(initialFirstVisibleItemIndex = selectedWeekIndex)

    // Generate weeks for current year
    val weeks = remember { generateWeeksForCurrentYear() }

    val scope = rememberCoroutineScope()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(text = "Budżet")
                },
                navigationIcon = {
                    IconButton(onClick = navigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Rounded.ArrowBack,
                            contentDescription = "Powrót"
                        )
                    }
                },
                actions = {
                    if (selectedWeekIndex != currentWeekIndex) {
                        IconButton(
                            onClick = {
                                selectedWeekIndex = currentWeekIndex
                                scope.launch {
                                    listState.animateScrollToItem(currentWeekIndex)
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Rounded.Today,
                                contentDescription = "Obecny tydzień"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Week selector
            LazyRow(
                state = listState,
                contentPadding = PaddingValues(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(vertical = 16.dp)
            ) {
                itemsIndexed(weeks) { index, week ->
                    WeekItem(
                        week = week,
                        isSelected = index == selectedWeekIndex,
                        isCurrentWeek = index == currentWeekIndex,
                        onClick = { selectedWeekIndex = index }
                    )
                }
            }

            Divider()

            // Main budget content
            BudgetContent(
                selectedWeek = weeks[selectedWeekIndex],
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            )
        }
    }
}

@Composable
private fun WeekItem(
    week: WeekData,
    isSelected: Boolean,
    isCurrentWeek: Boolean,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.width(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isSelected -> MaterialTheme.colorScheme.primaryContainer
                isCurrentWeek -> MaterialTheme.colorScheme.tertiaryContainer
                else -> MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else null
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Tydzień ${week.weekNumber}",
                    style = MaterialTheme.typography.labelMedium,
                    color = when {
                        isSelected -> MaterialTheme.colorScheme.onPrimaryContainer
                        isCurrentWeek -> MaterialTheme.colorScheme.onTertiaryContainer
                        else -> MaterialTheme.colorScheme.onSurface
                    }
                )
                if (isCurrentWeek && !isSelected) {
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.Rounded.Circle,
                        contentDescription = "Obecny tydzień",
                        modifier = Modifier.size(6.dp),
                        tint = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                }
            }
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = week.dateRange,
                style = MaterialTheme.typography.bodySmall,
                color = when {
                    isSelected -> MaterialTheme.colorScheme.onPrimaryContainer
                    isCurrentWeek -> MaterialTheme.colorScheme.onTertiaryContainer
                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                },
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun BudgetContent(
    selectedWeek: WeekData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.verticalScroll(rememberScrollState())
    ) {
        // Week info header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Tydzień ${selectedWeek.weekNumber}",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = selectedWeek.dateRange,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Budget section
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Budżet tygodniowy",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Current budget display
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Obecny budżet:",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "0,00 zł", // Placeholder
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "Ostatnia zmiana: Nie ustawiono", // Placeholder
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Set budget button
                Button(
                    onClick = { /* TODO: Open budget setting dialog */ },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Ustaw budżet")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // History section
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Historia zmian",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Placeholder for history
                Text(
                    text = "Brak zmian w tym tygodniu",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth().padding(vertical = 16.dp)
                )
            }
        }
    }
}

// Data classes and helper functions
data class WeekData(
    val weekNumber: Int,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val dateRange: String
)

private fun generateWeeksForCurrentYear(): List<WeekData> {
    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val currentYear = today.year
    val weeks = mutableListOf<WeekData>()

    // Start from first Monday of the year
    var date = LocalDate(currentYear, 1, 1)
    while (date.dayOfWeek != DayOfWeek.MONDAY) {
        date = date.plus(1, DateTimeUnit.DAY)
    }

    var weekNumber = 1
    while (date.year == currentYear) {
        val startOfWeek = date
        val endOfWeek = date.plus(6, DateTimeUnit.DAY)

        val dateRange = "${DateUtils.timestampToShortDate(DateUtils.localDateToTimestamp(startOfWeek))} - " +
                "${DateUtils.timestampToShortDate(DateUtils.localDateToTimestamp(endOfWeek))}"

        weeks.add(
            WeekData(
                weekNumber = weekNumber,
                startDate = startOfWeek,
                endDate = endOfWeek,
                dateRange = dateRange
            )
        )

        date = date.plus(7, DateTimeUnit.DAY)
        weekNumber++

        // Stop if we've moved to next year
        if (date.year != currentYear) break
    }

    return weeks
}

private fun getCurrentWeekIndex(): Int {
    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val weeks = generateWeeksForCurrentYear()

    return weeks.indexOfFirst { week ->
        today >= week.startDate && today <= week.endDate
    }.takeIf { it >= 0 } ?: 0
}


// BRAK ZAZNACZENIA OBECNEGO TYGODNIA
/*@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BudgetScreen(
    navigateBack: () -> Unit,
    viewModel: BudgetViewModel = koinViewModel()
) {
    var selectedWeekIndex by remember { mutableIntStateOf(getCurrentWeekIndex()) }
    val listState = rememberLazyListState(initialFirstVisibleItemIndex = selectedWeekIndex)

    // Generate weeks for current year
    val weeks = remember { generateWeeksForCurrentYear() }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(text = "Budżet")
                },
                navigationIcon = {
                    IconButton(onClick = navigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Rounded.ArrowBack,
                            contentDescription = "Powrót"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Week selector
            LazyRow(
                state = listState,
                contentPadding = PaddingValues(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(vertical = 16.dp)
            ) {
                itemsIndexed(weeks) { index, week ->
                    WeekItem(
                        week = week,
                        isSelected = index == selectedWeekIndex,
                        onClick = { selectedWeekIndex = index }
                    )
                }
            }

            Divider()

            // Main budget content
            BudgetContent(
                selectedWeek = weeks[selectedWeekIndex],
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            )
        }
    }
}

@Composable
private fun WeekItem(
    week: WeekData,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.width(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else null
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Tydzień ${week.weekNumber}",
                style = MaterialTheme.typography.labelMedium,
                color = if (isSelected) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurface
                }
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = week.dateRange,
                style = MaterialTheme.typography.bodySmall,
                color = if (isSelected) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun BudgetContent(
    selectedWeek: WeekData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.verticalScroll(rememberScrollState())
    ) {
        // Week info header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Tydzień ${selectedWeek.weekNumber}",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = selectedWeek.dateRange,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Budget section
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Budżet tygodniowy",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Current budget display
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Obecny budżet:",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "0,00 zł", // Placeholder
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "Ostatnia zmiana: Nie ustawiono", // Placeholder
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Set budget button
                Button(
                    onClick = { *//* TODO: Open budget setting dialog *//* },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Ustaw budżet")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // History section
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Historia zmian",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Placeholder for history
                Text(
                    text = "Brak zmian dla tego tygodnia",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth().padding(vertical = 16.dp)
                )
            }
        }
    }
}

// Data classes and helper functions
data class WeekData(
    val weekNumber: Int,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val dateRange: String
)

private fun generateWeeksForCurrentYear(): List<WeekData> {
    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val currentYear = today.year
    val weeks = mutableListOf<WeekData>()

    // Start from first Monday of the year
    var date = LocalDate(currentYear, 1, 1)
    while (date.dayOfWeek != DayOfWeek.MONDAY) {
        date = date.plus(1, DateTimeUnit.DAY)
    }

    var weekNumber = 1
    while (date.year == currentYear) {
        val startOfWeek = date
        val endOfWeek = date.plus(6, DateTimeUnit.DAY)

        val dateRange = "${DateUtils.timestampToShortDate(DateUtils.localDateToTimestamp(startOfWeek))} - " +
                "${DateUtils.timestampToShortDate(DateUtils.localDateToTimestamp(endOfWeek))}"

        weeks.add(
            WeekData(
                weekNumber = weekNumber,
                startDate = startOfWeek,
                endDate = endOfWeek,
                dateRange = dateRange
            )
        )

        date = date.plus(7, DateTimeUnit.DAY)
        weekNumber++

        // Stop if we've moved to next year
        if (date.year != currentYear) break
    }

    return weeks
}

private fun getCurrentWeekIndex(): Int {
    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val weeks = generateWeeksForCurrentYear()

    return weeks.indexOfFirst { week ->
        today >= week.startDate && today <= week.endDate
    }.takeIf { it >= 0 } ?: 0
}*/


/*
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BudgetScreen(
    navigateBack: () -> Unit,
    viewModel: BudgetViewModel = koinViewModel()
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(text = "Budżet")
                },
                navigationIcon = {
                    IconButton(onClick = navigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Rounded.ArrowBack,
                            contentDescription = "Powrót"
                        )
                    }
                }
            )
        }
    ) {
//        TODO on top vertical scrollable list of weeks in current year. On screen start list should be scrolled to current week, and current week should be selected. Selected week should be highlighted. By selecting week, user can see the budget for that week - its a main content of screen below the list of weeks - here user can see the budget for selected week, set this budget, see the date when that budget was set, and see the history of changes to that budget in that week.
    }
}*/
